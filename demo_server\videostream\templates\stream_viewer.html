
{% extends "base.html" %}
{% load static %}

{% block title %}Video Stream{% endblock %}

{% block content %}

{% block base_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<link rel="stylesheet" href="{% static 'videostream/css/stream_viewer.css' %}">
{% endblock %}

</head>
<body>
    <div class="container">
        {% block connection_header %}
        <header>
            <h1><i class="fas fa-video"></i> Video Stream</h1>
            <div class="connection-status">
                <span class="status-indicator disconnected" id="connection-indicator"></span>
                <span id="connection-text">Disconnected</span>
            </div>
        </header>
        {% endblock %}
        
        {% block main_content %}
        <div class="main-content">
            {% block camera_and_info %}
            <div>
                <div class="stream-container">
                    <img id="stream" class="stream-img" alt="Stream">
                    {% block canvas_element %}
                    {% endblock %}
                    <div id="frame-counter"><i class="fas fa-tachometer-alt"></i> 0 FPS</div>
                    <div class="loading-spinner"></div>
                    <div class="stream-overlay">
                        <div class="stream-info">
                            <span id="stream-resolution"></span>
                        </div>
                    </div>
                </div>
                
                <div class="status-panel">
                    <h3>Stream Status</h3>
                    <div id="status">Ready to start streaming</div>
                </div>
            </div>
            {% endblock %}

            <div class="controls-panel">
                {% block stream_start_controls %}
                <div class="control-group">
                    <h3>Stream Controls</h3>
                    <input type="hidden" id="stream-url" value="{{ stream_url|safe }}">
                    <input type="hidden" id="camera-id" value="{{ camera_id|default:'' }}">
                    <div class="button-group">
                        <button class="start-btn" onclick="startStream()">
                            <i class="fas fa-play"></i> Start
                        </button>
                        <button class="stop-btn" onclick="stopStream()">
                            <i class="fas fa-stop"></i> Stop
                        </button>
                    </div>
                </div>
                {% endblock %}

                {% block debug_info %}
                <div class="control-group">
                    <h3>Debug Information</h3>
                    <div id="debug-info">Debug information will appear here</div>
                </div>
                {% endblock %}

                {% block zone_creator %}
                {% endblock %}
            </div>
        </div>
        {% endblock %}
    </div>

    {% block stream_js %}
    <script src="{% static 'videostream/js/stream.js' %}"></script>
    {% endblock %}

    {% block page_utilities %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const streamImg = document.getElementById('stream');
            
            // Show loading state
            streamImg.addEventListener('loadstart', function() {
                streamImg.classList.add('loading');
            });
            
            streamImg.addEventListener('load', function() {
                streamImg.classList.remove('loading');
                document.getElementById('connection-indicator').classList.replace('disconnected', 'connected');
                document.getElementById('connection-text').textContent = 'Connected';
                
                // Update resolution information
                document.getElementById('stream-resolution').textContent = 
                    `${this.naturalWidth}x${this.naturalHeight}`;
            });
            
            streamImg.addEventListener('error', function() {
                document.getElementById('connection-indicator').classList.replace('connected', 'disconnected');
                document.getElementById('connection-text').textContent = 'Disconnected';
            });
            
            // Automatically start stream if URL is provided
            const streamUrl = document.getElementById('stream-url').value;
            if (streamUrl) {
                startStream();
            }
            
            // Zone filter activation (sadece zone create sayfasında değilse)
            setTimeout(function() {
                const cameraId = document.getElementById('camera-id').value;
                if (!isZoneCreatePage()) {
                    activateZoneFilter(cameraId);
                }
            }, 1000);        });
    </script>
    {% endblock %}

    {% block zone_creator_js %}
    {% endblock %}
    {% block view_zones_js %}
    {% endblock %}

{% endblock %}