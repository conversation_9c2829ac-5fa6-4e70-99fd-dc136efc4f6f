import logging
import traceback

# Import multiprocessing utilities for CUDA compatibility
from web_server.mp_utils import setup_multiprocessing

import numpy as np
from django.http import StreamingHttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from cameras.models import Camera
from django.shortcuts import get_object_or_404

from django.conf import settings

logger = logging.getLogger(__name__)

from django.db import models
from cameras.models import Camera
import json
from videostream.models import Zone
from videostream.zone_utils import process_zones
from videostream.managers import process_camera_manager


def generate_frames(camera_id):
    """Generate MJPEG frames for a specific camera"""
    # Convert camera_id to int for ProcessCameraManager
    camera_id = int(camera_id)
    
    while True:
        frame = process_camera_manager.get_frame(camera_id)
        if frame is not None:
            # ProcessCameraManager returns raw frame data, need to encode as JPEG
            import cv2
            ret, jpeg = cv2.imencode('.jpg', frame)
            if ret:
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + jpeg.tobytes() + b'\r\n')
        else:
            # If no frame available, yield a small delay
            import time
            time.sleep(0.01)


@login_required
def stream_viewer(request):
    """Render stream viewer page"""
    try:
        camera_id = request.GET.get('camera_id')
        stream_url = request.GET.get('camera_url', '')
        logger.info(f"Received stream URL: {stream_url}")

        context = {
            'stream_url': stream_url,
            'camera_id': camera_id
        }
        return render(request, 'stream_viewer.html', context)
    except Exception as e:
        logger.error(f"Stream viewer error: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)
    

@login_required
def zone_creator(request):
    """Render zone creator page"""
    try:
        camera_id = request.GET.get('camera_id')
        stream_url = request.GET.get('camera_url', '')
        logger.info(f"Received stream URL: {stream_url}")

        context = {
            'stream_url': stream_url,
            'camera_id': camera_id
        }

        return render(request, 'zone_creator.html', context)
    except Exception as e:
        logger.error(f"Zone creator error: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@login_required
def zone_request_handler(request):
    """Handle zone requests for a specific camera"""
    try:
        data = json.loads(request.body)
        camera_id = data.get('camera_id')
        submitted_zones = data.get('zones', [])

        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)
        
        # Convert camera_id to int
        camera_id = int(camera_id)
        
        camera = get_object_or_404(Camera, id=camera_id, user=request.user)

        # Process zones
        response_data = process_zones(camera, submitted_zones)

        return JsonResponse(response_data)
    except Exception as e:
        logger.error(f"Error saving zones: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)



@csrf_exempt
@login_required
def start_stream(request):
    """Start RTSP stream"""
    try:
        import json
        data = json.loads(request.body)
        url = data.get('url')
        camera_id = data.get('camera_id')
        model_disabled = data.get('model_disabled', False)  # Zone create sayfasında model devre dışı

        if not url:
            return JsonResponse({'error': 'No URL provided'}, status=400)
        
        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)

        # Convert camera_id to int
        camera_id = int(camera_id)

        # If camera_id is provided, get the camera object for face detection
        camera_obj = None
        try:
            camera_obj = get_object_or_404(Camera, id=camera_id, user=request.user)
            camera_obj.save()
        except Exception as e:
            logger.error(f"Error getting camera object: {str(e)}")
            # Continue without face detection if camera object can't be found
            pass

        process_camera_manager.start_camera_process(camera_id, url, camera_obj, model_disabled=model_disabled)

        return JsonResponse({'status': 'success'})
    except Exception as e:
        logger.error(f"Error starting stream: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@login_required
def stop_stream(request):
    """Stop RTSP stream"""
    try:
        # Handle both regular requests and sendBeacon requests
        if request.content_type == 'application/json' or request.body:
            try:
                data = json.loads(request.body) if request.body else {}
            except json.JSONDecodeError:
                # Handle sendBeacon requests that might not be proper JSON
                data = {}
        else:
            data = {}

        camera_id = data.get('camera_id')
        
        if camera_id:
            # Convert camera_id to int
            camera_id = int(camera_id)
            
            # Update the is_streaming status to False
            try:
                camera = Camera.objects.get(id=camera_id)
                camera.is_streaming = False
                camera.save()
            except Exception as e:
                logger.error(f"Error updating camera status: {str(e)}")

            process_camera_manager.stop_camera_process(camera_id)
        else:
            # If stopping all streams, update all cameras
            for camera_id in process_camera_manager.cameras.keys():
                try:
                    camera = Camera.objects.get(id=camera_id)
                    camera.is_streaming = False
                    camera.save()
                except Exception as e:
                    logger.error(f"Error updating camera {camera_id} status: {str(e)}")

            process_camera_manager.cleanup_all()
            
        return JsonResponse({'status': 'success'})
    except Exception as e:
        logger.error(f"Error stopping stream in stop_stream: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def mjpeg_stream(request):
    """Stream MJPEG frames"""
    camera_id = request.GET.get('camera_id')
    if not camera_id:
        return JsonResponse({'error': 'Camera ID is required'}, status=400)
    
    # Convert camera_id to int
    camera_id = int(camera_id)
        
    return StreamingHttpResponse(generate_frames(camera_id),
                                 content_type='multipart/x-mixed-replace; boundary=frame')

@login_required
def view_zones(request):
    """Render view zones page"""
    try:
        camera_id = request.GET.get('camera_id')
        stream_url = request.GET.get('camera_url', '')
        logger.info(f"Received stream URL: {stream_url}")

        context = {
            'stream_url': stream_url,
            'camera_id': camera_id
        }

        return render(request, 'view_zones.html', context)
    except Exception as e:
        logger.error(f"View zones error: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@login_required
def get_zones(request):
    """Fetch zones for a specific camera"""
    try:
        camera_id = request.GET.get('camera_id')
        
        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)
            
        # Convert camera_id to int
        camera_id = int(camera_id)
        
        camera = get_object_or_404(Camera, id=camera_id, user=request.user)
        zones = Zone.objects.filter(camera=camera)

        zones_data = [
            {'name': zone.name, 'points': zone.points, 'color': zone.color}
            for zone in zones
        ]

        return JsonResponse({'zones': zones_data})  # Return empty list if no zones
    except Exception as e:
        logger.error(f"Error fetching zones: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'zones': []})  # Return empty list on error

@csrf_exempt
@login_required
def toggle_zone_filter(request):
    """Toggle zone filtering on/off for a specific camera"""
    try:
        data = json.loads(request.body)
        camera_id = data.get('camera_id')
        filter_active = data.get('filter_active', False)
        
        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)
        
        # Convert camera_id to int
        camera_id = int(camera_id)
            
        # Check if camera process is running
        camera_info = process_camera_manager.get_camera(camera_id)
        if not camera_info or not camera_info.is_alive():
            return JsonResponse({'error': 'Camera not streaming'}, status=400)
        
        # Prepare zones data if filtering is active
        zones_data = []
        if filter_active:
            camera = get_object_or_404(Camera, id=camera_id, user=request.user)
            zones = Zone.objects.filter(camera=camera)
            zones_data = [
                {'name': zone.name, 'points': zone.points, 'color': zone.color}
                for zone in zones
            ]
        
        # Send command to camera process
        success = process_camera_manager.send_command_to_camera(
            camera_id, 
            "update_zones", 
            {
                "filter_by_zone": filter_active,
                "zones": zones_data
            }
        )
        
        if not success:
            return JsonResponse({'error': 'Failed to send command to camera process'}, status=500)
        
        return JsonResponse({'status': 'success', 'filter_active': filter_active})
    except Exception as e:
        logger.error(f"Error toggling zone filter: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@login_required
def update_fps(request):
    """Update FPS value for a camera"""
    try:
        data = json.loads(request.body)
        camera_id = data.get('camera_id')
        fps = data.get('fps', 0)

        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)

        camera = get_object_or_404(Camera, id=camera_id, user=request.user)
        camera.fps = fps
        camera.save()

        return JsonResponse({'status': 'success'})
    except Exception as e:
        logger.error(f"Error updating FPS: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)


# Initialize monitoring when the module loads
def initialize_process_manager():
    """Initialize process camera manager monitoring"""
    try:
        process_camera_manager.start_monitoring()
        logger.info("ProcessCameraManager monitoring initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing ProcessCameraManager monitoring: {e}")

# Start monitoring when module is imported
initialize_process_manager()

